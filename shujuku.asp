<%
' SQL Server 数据库连接配置
dim conn, connstr
dim server_name, database_name, username, password

' 数据库连接参数
server_name = "localhost"  ' 或者你的SQL Server服务器地址
database_name = "oa"       ' 数据库名称，请根据实际情况修改
username = "sa"
password = "secdriver"

' SQL Server 连接字符串
connstr = "Provider=SQLOLEDB;Data Source=" & server_name & ";Initial Catalog=" & database_name & ";User ID=" & username & ";Password=" & password & ";"

set conn=server.createobject("ADODB.CONNECTION")
on error resume next
conn.open connstr
if err.number <> 0 then
	response.write "数据库连接失败: " & err.description
	err.clear
end if
on error goto 0

Set rs = Server.CreateObject("ADODB.Recordset")
Set rs2 = Server.CreateObject("ADODB.Recordset")






'预设session
Session("name") = "JohnDoe"
Session("mingzhi") = "李小成"
%>
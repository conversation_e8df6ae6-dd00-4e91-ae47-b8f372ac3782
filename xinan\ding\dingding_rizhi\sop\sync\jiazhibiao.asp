<%@LANGUAGE："VBSCRIPT" CODEPAGE："65001"%>
<!--#include virtual："/shujuku.asp"-->

<!--
CREATE TABLE [dbo].[SOP测试商机价值评估表](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[商机id] [int] NULL,
	[客户系统与SOP适配性] [nvarchar](50) NULL,
	[客户系统与SOP适配性得分] [int] NULL,
	[关键决策人支持度] [nvarchar](50) NULL,
	[关键决策人支持度得分] [int] NULL,
	[用户预算情况] [nvarchar](50) NULL,
	[用户预算情况得分] [int] NULL,
	[SOP模式认可度] [nvarchar](50) NULL,
	[SOP模式认可度得分] [int] NULL,
	[用户互动积极性] [nvarchar](50) NULL,
	[用户互动积极性得分] [int] NULL,
	[继续测试积极性] [nvarchar](50) NULL,
	[继续测试积极性得分] [int] NULL,
	[漏洞整改重视程度] [nvarchar](50) NULL,
	[漏洞整改重视程度得分] [int] NULL,
	[总得分] [int] NULL,
	[评估结果] [nvarchar](100) NULL,
	[录入人] [nvarchar](50) NULL,
	[录入日期] [datetime] NULL,
	[备注] [nvarchar](max) NULL,
	[录入人直接主管] [nvarchar](50) NULL,
	[直接主管是否审核] [bit] NULL,
	[直接主管审核结果] [nvarchar](50) NULL,
	[直接主管审核意见] [nvarchar](max) NULL,
	[直接主管审核日期] [datetime] NULL,
	[更新日期] [datetime] NULL,
	[更新记录] [ntext] NULL
);
-->

<%
'获取表单参数
shangji_id = Request.form("shangji_id")
kehu_xitong_shipaixin = Request.form("kehu_xitong_shipaixin")
kehu_xitong_shipaixin_defen = Request.form("kehu_xitong_shipaixin_defen")
guanjian_jueceren_zhichidu = Request.form("guanjian_jueceren_zhichidu")
guanjian_jueceren_zhichidu_defen = Request.form("guanjian_jueceren_zhichidu_defen")
yonghu_yusuan_qingkuang = Request.form("yonghu_yusuan_qingkuang")
yonghu_yusuan_qingkuang_defen = Request.form("yonghu_yusuan_qingkuang_defen")
sop_moshi_renkegdu = Request.form("sop_moshi_renkegdu")
sop_moshi_renkegdu_defen = Request.form("sop_moshi_renkegdu_defen")
yonghu_hudong_jijixin = Request.form("yonghu_hudong_jijixin")
yonghu_hudong_jijixin_defen = Request.form("yonghu_hudong_jijixin_defen")
jixu_ceshi_jijixin = Request.form("jixu_ceshi_jijixin")
jixu_ceshi_jijixin_defen = Request.form("jixu_ceshi_jijixin_defen")
loudong_zhenggai_zhongshichengdu = Request.form("loudong_zhenggai_zhongshichengdu")
loudong_zhenggai_zhongshichengdu_defen = Request.form("loudong_zhenggai_zhongshichengdu_defen")
zong_defen = Request.form("zong_defen")
pinggu_jieguo = Request.form("pinggu_jieguo")
luru_ren = Request.form("luru_ren")
luru_riqi = Request.form("luru_riqi")
beizhu = Request.form("beizhu")
luru_ren_zhijie_zhuguan = Request.form("luru_ren_zhijie_zhuguan")
zhijie_zhuguan_shifou_shenhe = Request.form("zhijie_zhuguan_shifou_shenhe")
zhijie_zhuguan_shenhe_jieguo = Request.form("zhijie_zhuguan_shenhe_jieguo")
zhijie_zhuguan_shenhe_yijian = Request.form("zhijie_zhuguan_shenhe_yijian")
zhijie_zhuguan_shenhe_riqi = Request.form("zhijie_zhuguan_shenhe_riqi")

'检查必填参数 - 商机ID为必传
If shangji_id = "" Then
    Response.Write "{""code"":-1, ""msg"":""缺少商机ID参数""}"
    Response.End
End If

'查询当前记录的原始数据，用于生成更新记录
Dim query_sql, rs, old_record_str
query_sql = "SELECT * FROM [SOP测试商机价值评估表] WHERE [商机id] = " & shangji_id
Set rs = conn.Execute(query_sql)

If Not rs.EOF Then
    '构建原始记录字符串
    old_record_str = "更新前数据: "
    old_record_str = old_record_str & "客户系统与SOP适配性：" & rs("客户系统与SOP适配性") & "; "
    old_record_str = old_record_str & "客户系统与SOP适配性得分：" & rs("客户系统与SOP适配性得分") & "; "
    old_record_str = old_record_str & "关键决策人支持度：" & rs("关键决策人支持度") & "; "
    old_record_str = old_record_str & "关键决策人支持度得分：" & rs("关键决策人支持度得分") & "; "
    old_record_str = old_record_str & "用户预算情况：" & rs("用户预算情况") & "; "
    old_record_str = old_record_str & "用户预算情况得分：" & rs("用户预算情况得分") & "; "
    old_record_str = old_record_str & "SOP模式认可度：" & rs("SOP模式认可度") & "; "
    old_record_str = old_record_str & "SOP模式认可度得分：" & rs("SOP模式认可度得分") & "; "
    old_record_str = old_record_str & "用户互动积极性：" & rs("用户互动积极性") & "; "
    old_record_str = old_record_str & "用户互动积极性得分：" & rs("用户互动积极性得分") & "; "
    old_record_str = old_record_str & "继续测试积极性：" & rs("继续测试积极性") & "; "
    old_record_str = old_record_str & "继续测试积极性得分：" & rs("继续测试积极性得分") & "; "
    old_record_str = old_record_str & "漏洞整改重视程度：" & rs("漏洞整改重视程度") & "; "
    old_record_str = old_record_str & "漏洞整改重视程度得分：" & rs("漏洞整改重视程度得分") & "; "
    old_record_str = old_record_str & "总得分：" & rs("总得分") & "; "
    old_record_str = old_record_str & "评估结果：" & rs("评估结果") & "; "
    old_record_str = old_record_str & "录入人：" & rs("录入人") & "; "
    old_record_str = old_record_str & "录入日期：" & rs("录入日期") & "; "
    old_record_str = old_record_str & "备注：" & rs("备注") & "; "
    old_record_str = old_record_str & "录入人直接主管：" & rs("录入人直接主管") & "; "
    old_record_str = old_record_str & "直接主管是否审核：" & rs("直接主管是否审核") & "; "
    old_record_str = old_record_str & "直接主管审核结果：" & rs("直接主管审核结果") & "; "
    old_record_str = old_record_str & "直接主管审核意见：" & rs("直接主管审核意见") & "; "
    old_record_str = old_record_str & "直接主管审核日期：" & rs("直接主管审核日期")
Else
    Response.Write "{""code"":-1, ""msg"":""未找到对应的商机记录""}"
    Response.End
End If
rs.Close

'构建更新SQL
Dim update_sql
Dim update_parts()
Dim update_count
update_count = 0

'动态构建更新字段
If kehu_xitong_shipaixin <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[客户系统与SOP适配性] = '" & kehu_xitong_shipaixin & "'"
    update_count = update_count + 1
End If

If kehu_xitong_shipaixin_defen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[客户系统与SOP适配性得分] = " & kehu_xitong_shipaixin_defen
    update_count = update_count + 1
End If

If guanjian_jueceren_zhichidu <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[关键决策人支持度] = '" & guanjian_jueceren_zhichidu & "'"
    update_count = update_count + 1
End If

If guanjian_jueceren_zhichidu_defen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[关键决策人支持度得分] = " & guanjian_jueceren_zhichidu_defen
    update_count = update_count + 1
End If

If yonghu_yusuan_qingkuang <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[用户预算情况] = '" & yonghu_yusuan_qingkuang & "'"
    update_count = update_count + 1
End If

If yonghu_yusuan_qingkuang_defen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[用户预算情况得分] = " & yonghu_yusuan_qingkuang_defen
    update_count = update_count + 1
End If

If sop_moshi_renkegdu <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[SOP模式认可度] = '" & sop_moshi_renkegdu & "'"
    update_count = update_count + 1
End If

If sop_moshi_renkegdu_defen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[SOP模式认可度得分] = " & sop_moshi_renkegdu_defen
    update_count = update_count + 1
End If

If yonghu_hudong_jijixin <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[用户互动积极性] = '" & yonghu_hudong_jijixin & "'"
    update_count = update_count + 1
End If

If yonghu_hudong_jijixin_defen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[用户互动积极性得分] = " & yonghu_hudong_jijixin_defen
    update_count = update_count + 1
End If

If jixu_ceshi_jijixin <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[继续测试积极性] = '" & jixu_ceshi_jijixin & "'"
    update_count = update_count + 1
End If

If jixu_ceshi_jijixin_defen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[继续测试积极性得分] = " & jixu_ceshi_jijixin_defen
    update_count = update_count + 1
End If

If loudong_zhenggai_zhongshichengdu <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[漏洞整改重视程度] = '" & loudong_zhenggai_zhongshichengdu & "'"
    update_count = update_count + 1
End If

If loudong_zhenggai_zhongshichengdu_defen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[漏洞整改重视程度得分] = " & loudong_zhenggai_zhongshichengdu_defen
    update_count = update_count + 1
End If

If zong_defen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[总得分] = " & zong_defen
    update_count = update_count + 1
End If

If pinggu_jieguo <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[评估结果] = '" & pinggu_jieguo & "'"
    update_count = update_count + 1
End If

If luru_ren <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[录入人] = '" & luru_ren & "'"
    update_count = update_count + 1
End If

If luru_riqi <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[录入日期] = '" & luru_riqi & "'"
    update_count = update_count + 1
End If

If beizhu <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[备注] = '" & beizhu & "'"
    update_count = update_count + 1
End If

If luru_ren_zhijie_zhuguan <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[录入人直接主管] = '" & luru_ren_zhijie_zhuguan & "'"
    update_count = update_count + 1
End If

If zhijie_zhuguan_shifou_shenhe <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[直接主管是否审核] = " & zhijie_zhuguan_shifou_shenhe
    update_count = update_count + 1
End If

If zhijie_zhuguan_shenhe_jieguo <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[直接主管审核结果] = '" & zhijie_zhuguan_shenhe_jieguo & "'"
    update_count = update_count + 1
End If

If zhijie_zhuguan_shenhe_yijian <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[直接主管审核意见] = '" & zhijie_zhuguan_shenhe_yijian & "'"
    update_count = update_count + 1
End If

If zhijie_zhuguan_shenhe_riqi <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[直接主管审核日期] = '" & zhijie_zhuguan_shenhe_riqi & "'"
    update_count = update_count + 1
End If

'检查是否有字段需要更新
If update_count = 0 Then
    Response.Write "{""code"":-1,""msg"":""没有需要更新的字段""}"
    Response.End
End If

'添加自动维护的更新日期和更新记录
ReDim Preserve update_parts(update_count)
update_parts(update_count) = "[更新日期] = GETDATE()"
update_count = update_count + 1

ReDim Preserve update_parts(update_count)
update_parts(update_count) = "[更新记录] = '" & old_record_str & "'"
update_count = update_count + 1

'构建完整的更新SQL
update_sql = "UPDATE [SOP测试商机价值评估表] SET " & Join(update_parts, ", ") & " WHERE [商机id] = " & shangji_id

'执行更新操作
On Error Resume Next
conn.Execute update_sql
If Err.Number <> 0 Then
    Response.Write "{""code"":-1,""msg"":""更新失败: " & Err.Description & """}"
    Response.End
End If
On Error GoTo 0

' 返回成功响应
Response.Write "{""code"":1,""msg"":""更新记录成功""}"

%>


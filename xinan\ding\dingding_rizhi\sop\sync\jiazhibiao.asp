<%@LANGUAGE："VBSCRIPT" CODEPAGE："65001"%>
<!--#include virtual："/shujuku.asp"-->

<!--
CREATE TABLE [dbo].[SOP测试商机价值评估表](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[商机id] [int] NULL,
	[客户系统与SOP适配性] [nvarchar](50) NULL,
	[客户系统与SOP适配性得分] [int] NULL,
	[关键决策人支持度] [nvarchar](50) NULL,
	[关键决策人支持度得分] [int] NULL,
	[用户预算情况] [nvarchar](50) NULL,
	[用户预算情况得分] [int] NULL,
	[SOP模式认可度] [nvarchar](50) NULL,
	[SOP模式认可度得分] [int] NULL,
	[用户互动积极性] [nvarchar](50) NULL,
	[用户互动积极性得分] [int] NULL,
	[继续测试积极性] [nvarchar](50) NULL,
	[继续测试积极性得分] [int] NULL,
	[漏洞整改重视程度] [nvarchar](50) NULL,
	[漏洞整改重视程度得分] [int] NULL,
	[总得分] [int] NULL,
	[评估结果] [nvarchar](100) NULL,
	[录入人] [nvarchar](50) NULL,
	[录入日期] [datetime] NULL,
	[备注] [nvarchar](max) NULL,
	[录入人直接主管] [nvarchar](50) NULL,
	[直接主管是否审核] [bit] NULL,
	[直接主管审核结果] [nvarchar](50) NULL,
	[直接主管审核意见] [nvarchar](max) NULL,
	[直接主管审核日期] [datetime] NULL,
	[更新日期] [datetime] NULL,
	[更新记录] [ntext] NULL
);
-->

<%
'获取表单参数
shangji_id = Request.form("shangji_id")
sop_shipeixing = Request.form("sop_shipeixing")
sop_shipeixing_fen = Request.form("sop_shipeixing_fen")
zhichidu = Request.form("zhichidu")
zhichidu_fen = Request.form("zhichidu_fen")
yusuan = Request.form("yusuan")
yusuan_fen = Request.form("yusuan_fen")
sop_renkedu = Request.form("sop_renkedu")
sop_renkedu_fen = Request.form("sop_renkedu_fen")
jijixing = Request.form("jijixing")
jijixing_fen = Request.form("jijixing_fen")
ceshi_jijixing = Request.form("ceshi_jijixing")
ceshi_jijixing_fen = Request.form("ceshi_jijixing_fen")
zhongshi = Request.form("zhongshi")
zhongshi_fen = Request.form("zhongshi_fen")
zong_fen = Request.form("zong_fen")
pinggu_jieguo = Request.form("pinggu_jieguo")
luru_ren = Request.form("luru_ren")
luru_riqi = Request.form("luru_riqi")
beizhu = Request.form("beizhu")
zhuguan = Request.form("zhuguan")
is_check = Request.form("is_check")
check_result = Request.form("check_result")
check_suggest = Request.form("check_suggest")
check_time = Request.form("check_time")

'检查必填参数 - 商机ID为必传
If shangji_id = "" Then
    Response.Write "{""code"":-1, ""msg"":""缺少商机ID参数""}"
    Response.End
End If

'查询当前记录的原始数据，用于生成更新记录
Dim query_sql, rs, old_record_str, record_exists
query_sql = "SELECT * FROM [SOP测试商机价值评估表] WHERE [商机id] = " & shangji_id
Set rs = conn.Execute(query_sql)

If Not rs.EOF Then
    record_exists = True
    '构建原始记录字符串，包含上次更新时间
    old_record_str = "更新前数据: "
    old_record_str = old_record_str & "客户系统与SOP适配性：" & rs("客户系统与SOP适配性") & "; "
    old_record_str = old_record_str & "客户系统与SOP适配性得分：" & rs("客户系统与SOP适配性得分") & "; "
    old_record_str = old_record_str & "关键决策人支持度：" & rs("关键决策人支持度") & "; "
    old_record_str = old_record_str & "关键决策人支持度得分：" & rs("关键决策人支持度得分") & "; "
    old_record_str = old_record_str & "用户预算情况：" & rs("用户预算情况") & "; "
    old_record_str = old_record_str & "用户预算情况得分：" & rs("用户预算情况得分") & "; "
    old_record_str = old_record_str & "SOP模式认可度：" & rs("SOP模式认可度") & "; "
    old_record_str = old_record_str & "SOP模式认可度得分：" & rs("SOP模式认可度得分") & "; "
    old_record_str = old_record_str & "用户互动积极性：" & rs("用户互动积极性") & "; "
    old_record_str = old_record_str & "用户互动积极性得分：" & rs("用户互动积极性得分") & "; "
    old_record_str = old_record_str & "继续测试积极性：" & rs("继续测试积极性") & "; "
    old_record_str = old_record_str & "继续测试积极性得分：" & rs("继续测试积极性得分") & "; "
    old_record_str = old_record_str & "漏洞整改重视程度：" & rs("漏洞整改重视程度") & "; "
    old_record_str = old_record_str & "漏洞整改重视程度得分：" & rs("漏洞整改重视程度得分") & "; "
    old_record_str = old_record_str & "总得分：" & rs("总得分") & "; "
    old_record_str = old_record_str & "评估结果：" & rs("评估结果") & "; "
    old_record_str = old_record_str & "录入人：" & rs("录入人") & "; "
    old_record_str = old_record_str & "录入日期：" & rs("录入日期") & "; "
    old_record_str = old_record_str & "备注：" & rs("备注") & "; "
    old_record_str = old_record_str & "录入人直接主管：" & rs("录入人直接主管") & "; "
    old_record_str = old_record_str & "直接主管是否审核：" & rs("直接主管是否审核") & "; "
    old_record_str = old_record_str & "直接主管审核结果：" & rs("直接主管审核结果") & "; "
    old_record_str = old_record_str & "直接主管审核意见：" & rs("直接主管审核意见") & "; "
    old_record_str = old_record_str & "直接主管审核日期：" & rs("直接主管审核日期") & "; "
    old_record_str = old_record_str & "上次更新时间：" & rs("更新日期")
Else
    record_exists = False
    old_record_str = ""
End If
rs.Close

'构建更新SQL
Dim update_sql
Dim update_parts()
Dim update_count
update_count = 0

'动态构建更新字段
If sop_shipeixing <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[客户系统与SOP适配性] = '" & sop_shipeixing & "'"
    update_count = update_count + 1
End If

If sop_shipeixing_fen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[客户系统与SOP适配性得分] = " & sop_shipeixing_fen
    update_count = update_count + 1
End If

If zhichidu <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[关键决策人支持度] = '" & zhichidu & "'"
    update_count = update_count + 1
End If

If zhichidu_fen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[关键决策人支持度得分] = " & zhichidu_fen
    update_count = update_count + 1
End If

If yusuan <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[用户预算情况] = '" & yusuan & "'"
    update_count = update_count + 1
End If

If yusuan_fen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[用户预算情况得分] = " & yusuan_fen
    update_count = update_count + 1
End If

If sop_renkedu <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[SOP模式认可度] = '" & sop_renkedu & "'"
    update_count = update_count + 1
End If

If sop_renkedu_fen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[SOP模式认可度得分] = " & sop_renkedu_fen
    update_count = update_count + 1
End If

If jijixing <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[用户互动积极性] = '" & jijixing & "'"
    update_count = update_count + 1
End If

If jijixing_fen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[用户互动积极性得分] = " & jijixing_fen
    update_count = update_count + 1
End If

If ceshi_jijixing <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[继续测试积极性] = '" & ceshi_jijixing & "'"
    update_count = update_count + 1
End If

If ceshi_jijixing_fen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[继续测试积极性得分] = " & ceshi_jijixing_fen
    update_count = update_count + 1
End If

If zhongshi <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[漏洞整改重视程度] = '" & zhongshi & "'"
    update_count = update_count + 1
End If

If zhongshi_fen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[漏洞整改重视程度得分] = " & zhongshi_fen
    update_count = update_count + 1
End If

If zong_fen <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[总得分] = " & zong_fen
    update_count = update_count + 1
End If

If pinggu_jieguo <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[评估结果] = '" & pinggu_jieguo & "'"
    update_count = update_count + 1
End If

If luru_ren <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[录入人] = '" & luru_ren & "'"
    update_count = update_count + 1
End If

If luru_riqi <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[录入日期] = '" & luru_riqi & "'"
    update_count = update_count + 1
End If

If beizhu <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[备注] = '" & beizhu & "'"
    update_count = update_count + 1
End If

If zhuguan <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[录入人直接主管] = '" & zhuguan & "'"
    update_count = update_count + 1
End If

If is_check <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[直接主管是否审核] = " & is_check
    update_count = update_count + 1
End If

If check_result <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[直接主管审核结果] = '" & check_result & "'"
    update_count = update_count + 1
End If

If check_suggest <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[直接主管审核意见] = '" & check_suggest & "'"
    update_count = update_count + 1
End If

If check_time <> "" Then
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[直接主管审核日期] = '" & check_time & "'"
    update_count = update_count + 1
End If

'检查是否有字段需要操作
If update_count = 0 Then
    Response.Write "{""code"":-1,""msg"":""没有需要操作的字段""}"
    Response.End
End If

Dim sql_statement, success_msg

If record_exists Then
    '记录存在，执行更新操作
    '添加自动维护的更新日期和更新记录
    ReDim Preserve update_parts(update_count)
    update_parts(update_count) = "[更新日期] = GETDATE()"
    update_count = update_count + 1

    If old_record_str <> "" Then
        ReDim Preserve update_parts(update_count)
        update_parts(update_count) = "[更新记录] = '" & old_record_str & "'"
        update_count = update_count + 1
    End If

    '构建完整的更新SQL
    sql_statement = "UPDATE [SOP测试商机价值评估表] SET " & Join(update_parts, ", ") & " WHERE [商机id] = " & shangji_id
    success_msg = "更新记录成功"
Else
    '记录不存在，执行插入操作
    '构建插入SQL
    Dim insert_fields()
    Dim insert_values()
    Dim field_count
    field_count = 0

    '商机ID必须插入
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[商机id]"
    insert_values(field_count) = shangji_id
    field_count = field_count + 1

    '动态构建插入字段和值
    If sop_shipeixing <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[客户系统与SOP适配性]"
        insert_values(field_count) = "'" & sop_shipeixing & "'"
        field_count = field_count + 1
    End If

    If sop_shipeixing_fen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[客户系统与SOP适配性得分]"
        insert_values(field_count) = sop_shipeixing_fen
        field_count = field_count + 1
    End If

    If zhichidu <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[关键决策人支持度]"
        insert_values(field_count) = "'" & zhichidu & "'"
        field_count = field_count + 1
    End If

    If zhichidu_fen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[关键决策人支持度得分]"
        insert_values(field_count) = zhichidu_fen
        field_count = field_count + 1
    End If

    If yusuan <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[用户预算情况]"
        insert_values(field_count) = "'" & yusuan & "'"
        field_count = field_count + 1
    End If

    If yusuan_fen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[用户预算情况得分]"
        insert_values(field_count) = yusuan_fen
        field_count = field_count + 1
    End If

    If sop_renkedu <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[SOP模式认可度]"
        insert_values(field_count) = "'" & sop_renkedu & "'"
        field_count = field_count + 1
    End If

    If sop_renkedu_fen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[SOP模式认可度得分]"
        insert_values(field_count) = sop_renkedu_fen
        field_count = field_count + 1
    End If

    If jijixing <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[用户互动积极性]"
        insert_values(field_count) = "'" & jijixing & "'"
        field_count = field_count + 1
    End If

    If jijixing_fen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[用户互动积极性得分]"
        insert_values(field_count) = jijixing_fen
        field_count = field_count + 1
    End If

    If ceshi_jijixing <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[继续测试积极性]"
        insert_values(field_count) = "'" & ceshi_jijixing & "'"
        field_count = field_count + 1
    End If

    If ceshi_jijixing_fen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[继续测试积极性得分]"
        insert_values(field_count) = ceshi_jijixing_fen
        field_count = field_count + 1
    End If

    If zhongshi <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[漏洞整改重视程度]"
        insert_values(field_count) = "'" & zhongshi & "'"
        field_count = field_count + 1
    End If

    If zhongshi_fen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[漏洞整改重视程度得分]"
        insert_values(field_count) = zhongshi_fen
        field_count = field_count + 1
    End If

    If zong_fen <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[总得分]"
        insert_values(field_count) = zong_fen
        field_count = field_count + 1
    End If

    If pinggu_jieguo <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[评估结果]"
        insert_values(field_count) = "'" & pinggu_jieguo & "'"
        field_count = field_count + 1
    End If

    If luru_ren <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[录入人]"
        insert_values(field_count) = "'" & luru_ren & "'"
        field_count = field_count + 1
    End If

    If luru_riqi <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[录入日期]"
        insert_values(field_count) = "'" & luru_riqi & "'"
        field_count = field_count + 1
    End If

    If beizhu <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[备注]"
        insert_values(field_count) = "'" & beizhu & "'"
        field_count = field_count + 1
    End If

    If zhuguan <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[录入人直接主管]"
        insert_values(field_count) = "'" & zhuguan & "'"
        field_count = field_count + 1
    End If

    If is_check <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[直接主管是否审核]"
        insert_values(field_count) = is_check
        field_count = field_count + 1
    End If

    If check_result <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[直接主管审核结果]"
        insert_values(field_count) = "'" & check_result & "'"
        field_count = field_count + 1
    End If

    If check_suggest <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[直接主管审核意见]"
        insert_values(field_count) = "'" & check_suggest & "'"
        field_count = field_count + 1
    End If

    If check_time <> "" Then
        ReDim Preserve insert_fields(field_count)
        ReDim Preserve insert_values(field_count)
        insert_fields(field_count) = "[直接主管审核日期]"
        insert_values(field_count) = "'" & check_time & "'"
        field_count = field_count + 1
    End If

    '添加自动维护的更新日期
    ReDim Preserve insert_fields(field_count)
    ReDim Preserve insert_values(field_count)
    insert_fields(field_count) = "[更新日期]"
    insert_values(field_count) = "GETDATE()"
    field_count = field_count + 1

    '构建完整的插入SQL
    sql_statement = "INSERT INTO [SOP测试商机价值评估表] (" & Join(insert_fields, ", ") & ") VALUES (" & Join(insert_values, ", ") & ")"
    success_msg = "插入记录成功"
End If

'执行SQL操作
On Error Resume Next
conn.Execute sql_statement
If Err.Number <> 0 Then
    Response.Write "{""code"":-1,""msg"":""操作失败: " & Err.Description & """}"
    Response.End
End If
On Error GoTo 0

' 返回成功响应
Response.Write "{""code"":1,""msg"":""" & success_msg & """}"

%>

